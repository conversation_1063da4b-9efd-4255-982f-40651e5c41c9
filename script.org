* intro

Welcome everyone, its been a few months since the last video.
At that point, colony was just a dream, but now its here and ready to use
Pretty much everything we set out to do was accomplished.
This is the first graphical application to upload, download, and search for public files on the Autonomi network.
AND... open up Autonomi hosted websites.
This, my friends, is the moment you've been waiting for.
Let's dig into it.

* Navigate to the github page

This is the github page where you can find everything you need to get setup and running.
Note that this is also hosted on codeberg, see a link in the description, so if you aren't a fan of Microsoft, you can get to the code there as well.

Let's skim down to the installation.

Colony has binaries that you can use to just double click install for most linux distros and Windows.
For Mac users, I'm sorry, but I ran into a beaurocratic hurdle.
Basically, as an "uncertified" developer, Apple doesn't trust me and won't allow the binaries generated here to run on your Mac.
Eventually I'll look into paying them money to get certified because to Apple, certified just means I gave them cash.
For some linux distros, particularly those with the Wayland compositor and in particular, Arch and its derivatives, the AppImage isn't going to work either.
So for Mac folks and the linux users where the binaries don't work, you can build from source.
There are directions towards the bottom of the README that walk you through the necessary setup.

* Open the app for the first time

For this demo I'm on an Ubuntu flavor of linux.
So I'm going to download the AppImage, just because it is easier.
Download the file, make it executable, and open the app.
On Windows it is going to warn you about this being uncertified or unsafe, just ignore this and install it anyway.
Like Apple, Microsoft also wants a cut and I haven't paid their bribe.

* First boot process

After you download and do the initial install or build from source, you'll open the app.
It's going to show this welcome screen.
The first thing you need to do is enter a password.
Like anything else, make sure you remember it because you'll need it each time you login to the app.
If you forget your password neither me or anyone else can get back into the app.

* Seed phrase generation

Next you'll either generate or put in an existing 12 word seed phrase.
If you're familiar with cryptocurrencies this will be familiar.
If you're not, this set of words is used to recreate everything you put on the network.
So if you install Colony on another computer or say your computer crashes and you want to get all of your stuff back,
you will need these 12 words.
On the flip side, if anyone else gets these 12 words, they are you to the system, so make sure you never share these with anyone.
Write them down on paper, put them in a safe, just don't lose them.

So here I'm just going to generate a new account I'm not going to recover anything, so I'll hit generate and accept this new 12 word phrase.
Then I'll hit next.

* Seed phrase confirmation

Did you write those 12 words down? You didn't?
Because now you have to enter them in one more time just to make sure.
If you didn't write them down, you can go back to the previous screen and see what they were.

Since this is a junk account, I'll just go back a couple times.

OK, now i've got these entered and they match. If they don't, it will complain at you.
Hit next to get to the next step.

* wallet entry

Now we need to enter in an Ethereum compatible wallet private key.
The one filled in here is randomly generated and is good enough to use with small amounts.
Note that it is not tied to your seed phrase. This is a design decision.
In my opinion, you should always separate your money from your data.
That way if someone gets a hold of your data seed phrase, that we just entered, that person has access to your data only.
Same with your wallet private key, if someone gets a hold of your money, they have no way to get to your data.
That's just me, because I'm paranoid. You can do what you want.
If you're familiar with the ETH ecosystem, you can copy a private key from one of accounts in your metamask wallet or elsewhere.

* finish

And that's it. That was a lot of work wasn't it? If you're new to web3 or crypto! Congratulations!
If not, I hope this experience was no worse than any other app.

On this screen we've got this checkbox to automatically do a sync. If you want to use the app right away and have access to other data, leave that checked.
When you click finish, Colony will fetch any data you've already uploaded to the network, remember that seed phrase we entered?
If you don't have any data uploaded to Autonomi, Colony will pull down the initial set of data, which is essentially all publicly accesible files that I could find.

I'm going to click finish and its going to do its thing. Depending on how much stuff there is on the network, this can take a few minutes.


* App Configuration

After that initial sync you'll land on this page, the search page.
Before we do anything though, let's double check our configuration and make sure everything is to our liking
Click on the configuration tab.
The big thing here is the download path.
Windows and MacOS, the download path will be the system default, which is ok.
On linux however, there isn't a standard path on a lot of OS flavors, so I change this to something else.
You also have different themes you can try out.
I personally like the orange, which is why the defaults are what they are!
You can also change your password, pretty self explanatory.
In the top right corner you can cycle through the light and dark modes.
Dark mode is obviously superior, so I'm going to click that.
Auto will automatically follow your system settings, which works
on Windows and Mac, Linux, not so much, too much variation.

* App info

Now that we're configured, let's go check out the app info tab.
Here you'll find the developer info, links to all the major libraries
we used to make the app, source code, and what's this?
Donate icons? Oh no, how did those get in there?
Seriously though, making free open source software isn't easy
and takes a lot of time. If you use the app and like it, consider
sending some tokens. Colony has no ads and never will. There are no
servers tracking you around the internet. Big brother isn't watching over
your shoulder. Its just you and your computer, just as it should be.
But that means there is no way for me or anyone else to monetize Colony.
If you're someone who tips their waiter or barista for good service,
please consider tipping your friendly Colony developer. Or if you're
really generious, I'm loking for a late model Yaesu FTDX-9000MP with
matching VL-1000 amplifier. Yea, that would be awesome...

Anyway, onto search!

* Search screen

This functions pretty much just like any search engine you're familiar with.
The only difference is at this stage, there isn't much on the network,
so if you don't type anything, you'll see it says browse. This will
display everything on the network.

You'll remember that sync operation when we first started the app, well
that was downloading a search index of everything on the network. There is
no one tracking you because search is done 100% local on your computer.
Because of this, search operations are nearly instantaneous.

When you click on something, you'll see a popup with all the info about that
object.

If you want, you can download it.

You can also click the download icon and download directly, as a convenience.

The table can be sorted, etc.

Another useful feature is automatic loading of Autonomi sites. These are
websites fully loaded onto Autonomi. Let's look for Friends. When you click
on this one, you'll see it says open instead of download. If you click on it
it will pop up your web browser and load it for you.

Now I will say, the network is having some growing pains right now as I'm
filming this, so these sites take a LONG time to load. When you go to use this
it will be much faster. Quick shout out to my friend riddim who is making
this app Friends, if you haven't looked into this one, you should. It is
a truly peer to peer chat application that leverages Autonomi under the hood.
There is no middle man here unlike everything else out there. Great stuff.

Let's download a bunch of stuff.

* Status page

Now we've got a bunch of stuff downloading. Let's checkout where they are,
go to the status page. You'll see a timer running and it says downloading.
Today, the Autonomi libraries don't have a way to stream downloads. This
is being worked on and as soon as it is available, I'll plug this in. What
this means is that I can't give you the user a status update of where the
download is in terms of progress, just that it is still running.

Alright, some stuff has downloaded let's go check some of these out.

* Downloads

Your downloads will show up on the downloads page here. It tells you the address
where the file came from, how big it was, the date, and where it is. If you
click on the file name, it will open in the default system application.
This will be different depending on how you installed the application
and your OS.

* Finding more files

Now this is all good and well, but what if you want to find more stuff than
what we have here? If you have a friend that has uploaded stuff through colony
onto the Autonomi network, you can link to their stuff in the your pods page.

Now the first question you're going to ask is, what the heck is a pod? I know
it isn't the most intuitive thing, but let's walk through it.

A pod is a list of files you have uploaded to Autonomi and information about
them. Think about it like a book of recipes. There you have a page for each recipe
and then you list the ingredients and the directions to make the dish.
Here it is the same. We upload a file and then write down information about
the file so that the search engine can find it. The more information you can put
about the stuff you upload the better.

So a pod is just a list of files. But it can also list other pods. When you
list a pod as a reference, you're essentially creating a link to its content
as well. So when you do a search, you have access to all of the files in your
pod and all of the files in the pod you've referenced as well. But it doesn't
stop there. If you reference a pod that itself contains references, you'll
get all of those files and all of the files in any references there, on and on
forever. In this way, we don't have a central database, we just need
to connect to people that are connected to other people and the search
network builds itself.

Wow, that was heavy stuff. But that was the most complicated concept, its
easier from here on out.
At a high level, the more pod references you can
collect and the more people you can share your pods with, the more
interconnected and resiliant the network becomes.

So let's add one! If you look in the default pod that was created on install,
you'll see you already have a reference to a pod. This is how we were able to
do our initial search in the first place.
I happen to know that this pod has some stuff in it, so
I'll add it as a reference in one of my pods. I click on the edit button for
my pod, and then click the add pod ref button.

Now I have this reference locally, but I haven't pulled down the pod
from the network, so I don't have a file list yet. To prove it to you,
I'll go to the search screen and search for BLAH

See, nothing to be found.

I go back to the your pods screen and I click 'sync pods'. Now you'll see this
warning. If you have ever uploaded this pod to the network and you click
sync, it will wipe out your local edits with what was on the network. This is
your undo button. Since we have never uploaded this pod, we can safely ignore this
and proceed with the sync.

Now Colony is grabbing this new pod we referenced and pulling down this new
pod and any pods that it referenced.

Ok, now that's done, we go back to search and search for BLAH. And look,
now we can download this new file. Cool.

* Free mode

Up to now, there has been no need for crypto. On Autonomi, only uploads cost,
downloads are free. As you can see, there is a lot you can do without
having to spend any actual money here, and for a lot of people, this is all
you'll need to know. For those wanting to upload files and share information
with others, stick around, now we'll delve into uploads and more advanced
features of the colony ecosystem.

* Wallet

Before we get to uploads, we'll need to make sure our wallet is funded.
On the wallets page you can see the current balance of your default wallet.
You can also add additional wallets, edit existing ones, or remove them.
The wallet with the money bag is the 'default' wallet, the one that will
be used to upload files and your pods
